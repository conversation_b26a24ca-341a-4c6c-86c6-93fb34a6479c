import dayjs from "dayjs";
import isSameOrBeforePlugin from "dayjs/plugin/isSameOrBefore.js";
import timezone from "dayjs/plugin/timezone.js";
import utc from "dayjs/plugin/utc.js";
import { sql } from "kysely";
import { db } from "@/database/connection";
import { Env } from "@/shared/config/env.config";
import { LeaveRequestStatus, OfficeLeaveStatus } from "@/shared/enums";
import { PATHS } from "@/shared/lib/paths";
import type { AttendanceStatResponseDTO, DashboardResponseDTO } from "../dtos";

dayjs.extend(utc);
dayjs.extend(isSameOrBeforePlugin);
dayjs.extend(timezone);

// Helper untuk status colors sesuai frontend
const getLeaveRequestStatusColor = (status: string): string => {
	switch (status) {
		case LeaveRequestStatus.PENDING:
			return "yellow";
		case LeaveRequestStatus.APPROVED:
			return "green";
		case LeaveRequestStatus.REJECTED:
			return "red";
		default:
			return "yellow";
	}
};

const getOfficeLeaveStatusColor = (status: string): string => {
	switch (status) {
		case OfficeLeaveStatus.NEED_REVIEW:
			return "blue";
		case OfficeLeaveStatus.REVIEWED:
			return "green";
		default:
			return "blue";
	}
};

// Helper untuk status text sesuai frontend
const getLeaveRequestStatusText = (status: string): string => {
	switch (status) {
		case LeaveRequestStatus.PENDING:
			return "Perlu Persetujuan";
		case LeaveRequestStatus.APPROVED:
			return "Disetujui";
		case LeaveRequestStatus.REJECTED:
			return "Ditolak";
		default:
			return "Perlu Persetujuan";
	}
};

const getOfficeLeaveStatusText = (status: string): string => {
	switch (status) {
		case OfficeLeaveStatus.NEED_REVIEW:
			return "Butuh Review";
		case OfficeLeaveStatus.REVIEWED:
			return "Sudah Direview";
		default:
			return "Butuh Review";
	}
};

// Helper untuk generate avatar URL
const getAvatarUrl = (imagePath: string | null): string => {
	if (!imagePath) {
		return `${Env.SERVER_URL}${PATHS.toUrl(Env.DEFAULT_USER_IMAGE)}`;
	}
	return `${Env.SERVER_URL}${PATHS.toUrl(imagePath)}`;
};

// Helper untuk hitung trendType
const getTrendType = (value: number) =>
	value > 0 ? "increase" : value < 0 ? "decrease" : "neutral";

/**
 * Fungsi utility untuk convert tanggal client ke UTC boundaries
 */
export const getUTCBoundaries = (
	clientDate: string,
	clientTimezone: string,
) => {
	// Client date: "2024-01-15", Client timezone: "Asia/Jakarta"

	// Start of day dalam timezone client, lalu convert ke UTC
	const startOfDayUTC = dayjs
		.tz(`${clientDate} 00:00:00`, clientTimezone)
		.utc()
		.format();

	// End of day dalam timezone client, lalu convert ke UTC
	const endOfDayUTC = dayjs
		.tz(`${clientDate} 23:59:59.999`, clientTimezone)
		.utc()
		.format();

	return {
		startOfDayUTC, // "2024-01-14T17:00:00.000Z" (Jakarta 00:00 = UTC 17:00 kemarin)
		endOfDayUTC, // "2024-01-15T16:59:59.999Z" (Jakarta 23:59 = UTC 16:59 hari ini)
		clientDate, // "2024-01-15"
		clientTimezone, // "Asia/Jakarta"
	};
};

export const getDashboardData = async (
	timezone = "UTC",
): Promise<DashboardResponseDTO> => {
	const todayInClientTZ = dayjs().tz(timezone).format("YYYY-MM-DD");
	const yesterdayInClientTZ = dayjs()
		.tz(timezone)
		.subtract(1, "day")
		.format("YYYY-MM-DD");

	// Get UTC boundaries for today and yesterday in client timezone
	const todayBoundaries = getUTCBoundaries(todayInClientTZ, timezone);
	const yesterdayBoundaries = getUTCBoundaries(yesterdayInClientTZ, timezone);

	// === USERS DATA CALCULATIONS ===

	// Total employees
	const { total: totalEmployees } = await db
		.selectFrom("users")
		.select(sql<number>`count(*)`.as("total"))
		.executeTakeFirstOrThrow();

	// Total employees registered before today (in client timezone)
	const { total: totalEmployeesYesterday } = await db
		.selectFrom("users")
		.where("createdAt", "<", todayBoundaries.startOfDayUTC) // Sebelum tanggal client dimulai
		.select(sql<number>`count(*)`.as("total"))
		.executeTakeFirstOrThrow();

	// Employees present today (in client timezone)
	const { total: totalPresent } = await db
		.selectFrom("attendanceLogs")
		.where("createdAt", ">=", todayBoundaries.startOfDayUTC)
		.where("createdAt", "<=", todayBoundaries.endOfDayUTC)
		.where("type", "=", "CHECK_IN")
		.select(sql<number>`count(distinct user_id)`.as("total"))
		.executeTakeFirstOrThrow();

	// Employees present yesterday (in client timezone)
	const { total: totalPresentYesterday } = await db
		.selectFrom("attendanceLogs")
		.where("createdAt", ">=", yesterdayBoundaries.startOfDayUTC)
		.where("createdAt", "<=", yesterdayBoundaries.endOfDayUTC)
		.where("type", "=", "CHECK_IN")
		.select(sql<number>`count(distinct user_id)`.as("total"))
		.executeTakeFirstOrThrow();

	// Total employees not present today (total employees - present employees)
	const totalNotPresent = totalEmployees - totalPresent;
	const totalNotPresentYesterday =
		totalEmployeesYesterday - totalPresentYesterday;

	// Total employees on leave today (approved leave requests for client timezone date)
	const { total: totalUsersLeave } = await db
		.selectFrom("leaveRequests")
		.where("status", "=", "APPROVED")
		.where("startDate", "<=", todayInClientTZ)
		.where("endDate", ">=", todayInClientTZ)
		.select(sql<number>`count(distinct user_id)`.as("total"))
		.executeTakeFirstOrThrow();

	// Total employees on leave yesterday (approved leave requests for client timezone date)
	const { total: totalUsersLeaveYesterday } = await db
		.selectFrom("leaveRequests")
		.where("status", "=", "APPROVED")
		.where("startDate", "<=", yesterdayInClientTZ)
		.where("endDate", ">=", yesterdayInClientTZ)
		.select(sql<number>`count(distinct user_id)`.as("total"))
		.executeTakeFirstOrThrow();

	// === ATTENDANCE DATA CALCULATIONS ===

	// Get attendance rules for determining on-time vs late
	const attendanceRules = await db
		.selectFrom("attendanceRules")
		.select(["checkInEndTime", "checkOutStartTime"])
		.executeTakeFirst();

	const checkInEndTime = attendanceRules?.checkInEndTime || "09:00:00";
	const checkOutStartTime = attendanceRules?.checkOutStartTime || "17:00:00";

	// Total employees who came on time today (in client timezone)
	const { total: totalOnTime } = await db
		.selectFrom("attendanceLogs")
		.where("createdAt", ">=", todayBoundaries.startOfDayUTC)
		.where("createdAt", "<=", todayBoundaries.endOfDayUTC)
		.where("type", "=", "CHECK_IN")
		.where("logTime", "<=", checkInEndTime)
		.select(sql<number>`count(*)`.as("total"))
		.executeTakeFirstOrThrow();

	// Total employees who came on time yesterday (in client timezone)
	const { total: totalOnTimeYesterday } = await db
		.selectFrom("attendanceLogs")
		.where("createdAt", ">=", yesterdayBoundaries.startOfDayUTC)
		.where("createdAt", "<=", yesterdayBoundaries.endOfDayUTC)
		.where("type", "=", "CHECK_IN")
		.where("logTime", "<=", checkInEndTime)
		.select(sql<number>`count(*)`.as("total"))
		.executeTakeFirstOrThrow();

	// Total employees who came late today (in client timezone)
	const { total: totalLate } = await db
		.selectFrom("attendanceLogs")
		.where("createdAt", ">=", todayBoundaries.startOfDayUTC)
		.where("createdAt", "<=", todayBoundaries.endOfDayUTC)
		.where("type", "=", "CHECK_IN")
		.where("logTime", ">", checkInEndTime)
		.select(sql<number>`count(*)`.as("total"))
		.executeTakeFirstOrThrow();

	// Total employees who came late yesterday (in client timezone)
	const { total: totalLateYesterday } = await db
		.selectFrom("attendanceLogs")
		.where("createdAt", ">=", yesterdayBoundaries.startOfDayUTC)
		.where("createdAt", "<=", yesterdayBoundaries.endOfDayUTC)
		.where("type", "=", "CHECK_IN")
		.where("logTime", ">", checkInEndTime)
		.select(sql<number>`count(*)`.as("total"))
		.executeTakeFirstOrThrow();

	// Total employees who checked out on time today (in client timezone)
	const { total: totalCheckoutOnTime } = await db
		.selectFrom("attendanceLogs")
		.where("createdAt", ">=", todayBoundaries.startOfDayUTC)
		.where("createdAt", "<=", todayBoundaries.endOfDayUTC)
		.where("type", "=", "CHECK_OUT")
		.where("logTime", ">=", checkOutStartTime)
		.select(sql<number>`count(*)`.as("total"))
		.executeTakeFirstOrThrow();

	// Total employees who checked out on time yesterday (in client timezone)
	const { total: totalCheckoutOnTimeYesterday } = await db
		.selectFrom("attendanceLogs")
		.where("createdAt", ">=", yesterdayBoundaries.startOfDayUTC)
		.where("createdAt", "<=", yesterdayBoundaries.endOfDayUTC)
		.where("type", "=", "CHECK_OUT")
		.where("logTime", ">=", checkOutStartTime)
		.select(sql<number>`count(*)`.as("total"))
		.executeTakeFirstOrThrow();

	// Total employees who checked out early today (in client timezone)
	const { total: totalCheckoutEarly } = await db
		.selectFrom("attendanceLogs")
		.where("createdAt", ">=", todayBoundaries.startOfDayUTC)
		.where("createdAt", "<=", todayBoundaries.endOfDayUTC)
		.where("type", "=", "CHECK_OUT")
		.where("logTime", "<", checkOutStartTime)
		.select(sql<number>`count(*)`.as("total"))
		.executeTakeFirstOrThrow();

	// Total employees who checked out early yesterday (in client timezone)
	const { total: totalCheckoutEarlyYesterday } = await db
		.selectFrom("attendanceLogs")
		.where("createdAt", ">=", yesterdayBoundaries.startOfDayUTC)
		.where("createdAt", "<=", yesterdayBoundaries.endOfDayUTC)
		.where("type", "=", "CHECK_OUT")
		.where("logTime", "<", checkOutStartTime)
		.select(sql<number>`count(*)`.as("total"))
		.executeTakeFirstOrThrow();

	// === LATEST LEAVE REQUESTS ===
	const latestLeaveRequests = await db
		.selectFrom("leaveRequests")
		.innerJoin("users", "users.id", "leaveRequests.userId")
		.innerJoin(
			"leavePolicies",
			"leavePolicies.id",
			"leaveRequests.leavePolicyId",
		)
		.leftJoin("users as reviewer", "reviewer.id", "leaveRequests.reviewedBy")
		.selectAll("leaveRequests")
		.select([
			"leavePolicies.name",
			"leavePolicies.isCountedAsPresent",
			"users.name as userName",
			"users.email as userEmail",
			"users.image as userImage",
			"reviewer.name as reviewerName",
			"reviewer.email as reviewerEmail",
		])
		.orderBy("leaveRequests.createdAt", "desc")
		.limit(3)
		.execute();

	// === LATEST OFFICE LEAVES ===
	const latestOfficeLeaves = await db
		.selectFrom("officeLeaves")
		.innerJoin("users", "users.id", "officeLeaves.userId")
		.leftJoin("users as reviewer", "reviewer.id", "officeLeaves.reviewedBy")
		.selectAll("officeLeaves")
		.select([
			"users.name as userName",
			"users.email as userEmail",
			"users.image as userImage",
			"reviewer.name as reviewerName",
			"reviewer.email as reviewerEmail",
		])
		.orderBy("officeLeaves.createdAt", "desc")
		.limit(3)
		.execute();

	return {
		usersData: [
			{
				id: "total_employees",
				title: "Total Karyawan",
				total: totalEmployees,
				trend: {
					value: totalEmployees - totalEmployeesYesterday,
					description:
						totalEmployees > totalEmployeesYesterday
							? "Meningkat dibanding kemarin"
							: totalEmployees < totalEmployeesYesterday
								? "Menurun dibanding kemarin"
								: "Sama seperti kemarin",
					trendType: getTrendType(totalEmployees - totalEmployeesYesterday),
				},
				iconKey: "users_double_blue",
			},
			{
				id: "total_present",
				title: "Total Karyawan Hadir",
				total: totalPresent,
				trend: {
					value: totalPresent - totalPresentYesterday,
					description:
						totalPresent > totalPresentYesterday
							? "Meningkat dibanding kemarin"
							: totalPresent < totalPresentYesterday
								? "Menurun dibanding kemarin"
								: "Sama seperti kemarin",
					trendType: getTrendType(totalPresent - totalPresentYesterday),
				},
				iconKey: "users_double_green",
			},
			{
				id: "total_not_present",
				title: "Total Karyawan Tidak Hadir",
				total: totalNotPresent,
				trend: {
					value: totalNotPresent - totalNotPresentYesterday,
					description:
						totalNotPresent > totalNotPresentYesterday
							? "Meningkat dibanding kemarin"
							: totalNotPresent < totalNotPresentYesterday
								? "Menurun dibanding kemarin"
								: "Sama seperti kemarin",
					trendType: getTrendType(totalNotPresent - totalNotPresentYesterday),
				},
				iconKey: "users_double_red",
			},
			{
				id: "total_users_leave",
				title: "Total Karyawan Cuti",
				total: totalUsersLeave,
				trend: {
					value: totalUsersLeave - totalUsersLeaveYesterday,
					description:
						totalUsersLeave > totalUsersLeaveYesterday
							? "Meningkat dibanding kemarin"
							: totalUsersLeave < totalUsersLeaveYesterday
								? "Menurun dibanding kemarin"
								: "Sama seperti kemarin",
					trendType: getTrendType(totalUsersLeave - totalUsersLeaveYesterday),
				},
				iconKey: "users_double_yellow",
			},
		],
		attendanceData: [
			{
				id: "total_on_time",
				title: "Total Karyawan Datang Tepat Waktu",
				total: totalOnTime,
				trend: {
					value: totalOnTime - totalOnTimeYesterday,
					description:
						totalOnTime > totalOnTimeYesterday
							? "Meningkat dibanding kemarin"
							: totalOnTime < totalOnTimeYesterday
								? "Menurun dibanding kemarin"
								: "Sama seperti kemarin",
					trendType: getTrendType(totalOnTime - totalOnTimeYesterday),
				},
				iconKey: "timer_green",
			},
			{
				id: "total_late",
				title: "Total Karyawan Datang Terlambat",
				total: totalLate,
				trend: {
					value: totalLate - totalLateYesterday,
					description:
						totalLate > totalLateYesterday
							? "Meningkat dibanding kemarin"
							: totalLate < totalLateYesterday
								? "Menurun dibanding kemarin"
								: "Sama seperti kemarin",
					trendType: getTrendType(totalLate - totalLateYesterday),
				},
				iconKey: "timer_red",
			},
			{
				id: "total_checkout_on_time",
				title: "Total Karyawan Pulang Tepat Waktu",
				total: totalCheckoutOnTime,
				trend: {
					value: totalCheckoutOnTime - totalCheckoutOnTimeYesterday,
					description:
						totalCheckoutOnTime > totalCheckoutOnTimeYesterday
							? "Meningkat dibanding kemarin"
							: totalCheckoutOnTime < totalCheckoutOnTimeYesterday
								? "Menurun dibanding kemarin"
								: "Sama seperti kemarin",
					trendType: getTrendType(
						totalCheckoutOnTime - totalCheckoutOnTimeYesterday,
					),
				},
				iconKey: "timer_green",
			},
			{
				id: "total_checkout_early",
				title: "Total Karyawan Pulang Awal",
				total: totalCheckoutEarly,
				trend: {
					value: totalCheckoutEarly - totalCheckoutEarlyYesterday,
					description:
						totalCheckoutEarly > totalCheckoutEarlyYesterday
							? "Meningkat dibanding kemarin"
							: totalCheckoutEarly < totalCheckoutEarlyYesterday
								? "Menurun dibanding kemarin"
								: "Sama seperti kemarin",
					trendType: getTrendType(
						totalCheckoutEarly - totalCheckoutEarlyYesterday,
					),
				},
				iconKey: "timer_red",
			},
		],
		latestLeaveRequests: latestLeaveRequests.map((leave) => ({
			id: leave.id,
			date: dayjs(leave.createdAt)
				.tz(timezone)
				.locale("id")
				.format("D MMMM YYYY"),
			name: leave.userName,
			status: getLeaveRequestStatusText(leave.status),
			statusColor: getLeaveRequestStatusColor(leave.status),
			avatar: getAvatarUrl(leave.userImage || null),
			captionLabel: "Cuti",
			caption: leave.name, // nama kebijakan cuti
		})),
		latestOfficeLeaves: latestOfficeLeaves.map((officeLeave) => ({
			id: officeLeave.id,
			date: dayjs(officeLeave.createdAt)
				.tz(timezone)
				.locale("id")
				.format("D MMMM YYYY"),
			name: officeLeave.userName,
			status: getOfficeLeaveStatusText(officeLeave.status),
			statusColor: getOfficeLeaveStatusColor(officeLeave.status),
			avatar: getAvatarUrl(officeLeave.userImage || null),
			captionLabel: "Izin Keluar",
			caption: officeLeave.title,
		})),
	};
};

export const getAttendanceStat = async (
	startDate?: string,
	endDate?: string,
	timezone = "UTC",
): Promise<AttendanceStatResponseDTO[]> => {
	// Get today's date in client timezone
	const todayInClientTZ = dayjs().tz(timezone).format("YYYY-MM-DD");

	// Default: 7 hari ke belakang dari hari ini (dalam timezone client)
	const defaultStartDateInClientTZ = dayjs()
		.tz(timezone)
		.subtract(6, "day") // 6 hari ke belakang + hari ini = 7 hari total
		.format("YYYY-MM-DD");

	// Validate and set default dates
	let defaultStartDate: string;
	let defaultEndDate: string;

	// Check if startDate is valid
	if (startDate && dayjs(startDate).isValid()) {
		defaultStartDate = dayjs(startDate).format("YYYY-MM-DD");
	} else {
		defaultStartDate = defaultStartDateInClientTZ; // 7 hari ke belakang
	}

	// Check if endDate is valid
	if (endDate && dayjs(endDate).isValid()) {
		defaultEndDate = dayjs(endDate).format("YYYY-MM-DD");
	} else {
		defaultEndDate = todayInClientTZ; // hari ini
	}

	// Ensure startDate is not after endDate
	if (dayjs(defaultStartDate).isAfter(dayjs(defaultEndDate))) {
		defaultStartDate = defaultEndDate;
	}

	// Generate array of dates between startDate and endDate using dayjs
	const dates: string[] = [];
	const start = dayjs(defaultStartDate);
	const end = dayjs(defaultEndDate);

	let currentDate = start;
	while (currentDate.isSameOrBefore(end, "day")) {
		dates.push(currentDate.format("YYYY-MM-DD"));
		currentDate = currentDate.add(1, "day");
	}

	// Get total employees count (this should be consistent across dates for simplicity)
	const { total: totalEmployees } = await db
		.selectFrom("users")
		.select(sql<number>`count(*)`.as("total"))
		.executeTakeFirstOrThrow();

	// Get attendance data for all dates in the range using UTC boundaries
	const attendanceData: { date: string; presentCount: number }[] = [];

	for (const date of dates) {
		const dateBoundaries = getUTCBoundaries(date, timezone);

		const { total: presentCount } = await db
			.selectFrom("attendanceLogs")
			.where("createdAt", ">=", dateBoundaries.startOfDayUTC)
			.where("createdAt", "<=", dateBoundaries.endOfDayUTC)
			.where("type", "=", "CHECK_IN")
			.select(sql<number>`count(distinct user_id)`.as("total"))
			.executeTakeFirstOrThrow();

		attendanceData.push({ date, presentCount });
	}

	// Get leave data for all dates in the range (using client timezone dates for leave requests)
	const leaveData: { leaveDate: string; leaveCount: number }[] = [];

	// For each date, count users on leave
	for (const date of dates) {
		const { total: leaveCount } = await db
			.selectFrom("leaveRequests")
			.select(sql<number>`count(distinct user_id)`.as("total"))
			.where("status", "=", "APPROVED")
			.where("startDate", "<=", date)
			.where("endDate", ">=", date)
			.executeTakeFirstOrThrow();

		leaveData.push({ leaveDate: date, leaveCount });
	}

	// Create a map for quick lookup
	const attendanceMap = new Map<string, number>();
	attendanceData.forEach((row) => {
		attendanceMap.set(row.date, row.presentCount);
	});

	const leaveMap = new Map<string, number>();
	leaveData.forEach((row) => {
		leaveMap.set(row.leaveDate, row.leaveCount);
	});

	// Build the result array
	const result: AttendanceStatResponseDTO[] = dates.map((date) => {
		const present = attendanceMap.get(date) || 0;
		const leave = leaveMap.get(date) || 0;
		const absent = Math.max(0, totalEmployees - present - leave);

		return {
			date,
			total: totalEmployees,
			present,
			absent,
			leave,
		};
	});

	return result;
};
